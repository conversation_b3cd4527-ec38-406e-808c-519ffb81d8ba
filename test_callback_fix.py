#!/usr/bin/env python3
"""
Test script to verify the candle callback mechanism is working
"""

import asyncio
import logging
import os
from datetime import datetime, timezone, timedelta
from utils.enhanced_websocket_service import EnhancedWebSocketService

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Test callback function
async def test_callback(candle_data):
    """Test callback function to verify callbacks are working"""
    logger.info(f"🎯 [TEST_CALLBACK] Received candle: {candle_data['symbol']} {candle_data['timeframe']} at {candle_data['timestamp']}")
    logger.info(f"🎯 [TEST_CALLBACK] OHLCV: O={candle_data['open']:.2f} H={candle_data['high']:.2f} L={candle_data['low']:.2f} C={candle_data['close']:.2f} V={candle_data['volume']}")

async def main():
    """Main test function"""
    logger.info("🚀 Starting callback test...")

    # Get credentials from environment or config
    try:
        from config.config import Config
        config = Config()

        # Create enhanced websocket service with minimal symbols for testing
        test_symbols = {'RELIANCE': '2885', 'TCS': '11536', 'INFY': '1594'}
        service = EnhancedWebSocketService(
            auth_token=config.AUTH_TOKEN,
            api_key=config.API_KEY,
            username=config.USERNAME,
            feed_token=config.FEED_TOKEN,
            selected_symbols=test_symbols
        )

        # Register test callback
        service.add_candle_callback(test_callback)
        logger.info("✅ Test callback registered")

        # Start the service
        await service.start()
        logger.info("✅ Enhanced WebSocket service started")

        # Let it run for a few minutes to see if callbacks are triggered
        logger.info("⏳ Waiting for candle callbacks... (will run for 3 minutes)")
        await asyncio.sleep(180)  # 3 minutes

        # Stop the service
        await service.stop()
        logger.info("🛑 Test completed")

    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")

if __name__ == "__main__":
    asyncio.run(main())
