# ✅ Enhanced Strategy Configuration for Indian Market
# Based on most popular & profitable strategies used by Indian traders
# Optimized for NSE/BSE intraday & swing trading with 30 proven strategies

strategies:
  # ═══════════════════════════════════════════════════════════════════════════════
  # 🔥 MOMENTUM STRATEGIES (Most Popular in Indian Market)
  # ═══════════════════════════════════════════════════════════════════════════════

  - name: "CPR_Breakout_Strategy"
    long: "close > cpr_top & volume > volume.rolling_mean(20) * 1.5 & rsi_14 > 50"
    short: "close < cpr_bottom & volume > volume.rolling_mean(20) * 1.5 & rsi_14 < 50"
    capital: 100000

  - name: "VWAP_Pullback_Strategy"
    long: "close > vwap & low <= vwap * 0.998 & volume > volume.rolling_mean(20) & rsi_14 > 45"
    short: "close < vwap & high >= vwap * 1.002 & volume > volume.rolling_mean(20) & rsi_14 < 55"
    capital: 100000

  - name: "SuperTrend_EMA_Strategy"
    long: "close > supertrend & close > ema_20 & ema_5 > ema_21 & volume > volume.rolling_mean(10) * 1.2"
    short: "close < supertrend & close < ema_20 & ema_5 < ema_21 & volume > volume.rolling_mean(10) * 1.2"
    capital: 100000

  - name: "Opening_Range_Breakout"
    long: "close > high.rolling_max(15).shift(1) & volume > volume.rolling_mean(20) * 2.0 & rsi_14 > 50"
    short: "close < low.rolling_min(15).shift(1) & volume > volume.rolling_mean(20) * 2.0 & rsi_14 < 50"
    capital: 100000

  - name: "EMA_Crossover_Momentum"
    long: "ema_5 > ema_21 & ema_5.shift(1) <= ema_21.shift(1) & volume > volume.rolling_mean(20) * 1.3 & rsi_14 > 50"
    short: "ema_5 < ema_21 & ema_5.shift(1) >= ema_21.shift(1) & volume > volume.rolling_mean(20) * 1.3 & rsi_14 < 50"
    capital: 100000

  # ═══════════════════════════════════════════════════════════════════════════════
  # 📈 SCALPING STRATEGIES (High Frequency Indian Market)
  # ═══════════════════════════════════════════════════════════════════════════════

  - name: "EMA_5_13_Scalping"
    long: "ema_5 > ema_13 & ema_5.shift(1) <= ema_13.shift(1) & volume > volume.rolling_mean(5) * 1.5"
    short: "ema_5 < ema_13 & ema_5.shift(1) >= ema_13.shift(1) & volume > volume.rolling_mean(5) * 1.5"
    capital: 100000

  - name: "RSI_Scalping_Strategy"
    long: "rsi_14 > 50 & rsi_14.shift(1) <= 50 & close > ema_5 & volume > volume.rolling_mean(10)"
    short: "rsi_14 < 50 & rsi_14.shift(1) >= 50 & close < ema_5 & volume > volume.rolling_mean(10)"
    capital: 100000

  - name: "MACD_Signal_Scalping"
    long: "macd > macd_signal & macd.shift(1) <= macd_signal.shift(1) & close > ema_20"
    short: "macd < macd_signal & macd.shift(1) >= macd_signal.shift(1) & close < ema_20"
    capital: 100000

  - name: "Bollinger_Squeeze_Scalping"
    long: "close < bb_lower & rsi_14 < 35 & volume > volume.rolling_mean(10) * 1.3"
    short: "close > bb_upper & rsi_14 > 65 & volume > volume.rolling_mean(10) * 1.3"
    capital: 100000

  - name: "Volume_Price_Scalping"
    long: "volume > volume.rolling_mean(5) * 2.0 & close > open & close > ema_5"
    short: "volume > volume.rolling_mean(5) * 2.0 & close < open & close < ema_5"
    capital: 100000

  # ═══════════════════════════════════════════════════════════════════════════════
  # 🎯 REVERSAL STRATEGIES (Mean Reversion)
  # ═══════════════════════════════════════════════════════════════════════════════

  - name: "RSI_Oversold_Reversal"
    long: "rsi_14 < 30 & rsi_14.shift(1) >= 30 & close > low.rolling_min(5) * 1.005"
    short: "rsi_14 > 70 & rsi_14.shift(1) <= 70 & close < high.rolling_max(5) * 0.995"
    capital: 100000

  - name: "Stochastic_Reversal"
    long: "stoch_k < 20 & stoch_k > stoch_d & stoch_k.shift(1) <= stoch_d.shift(1)"
    short: "stoch_k > 80 & stoch_k < stoch_d & stoch_k.shift(1) >= stoch_d.shift(1)"
    capital: 100000

  - name: "CCI_Reversal_Strategy"
    long: "cci < -100 & cci.shift(1) >= -100 & close > ema_20"
    short: "cci > 100 & cci.shift(1) <= 100 & close < ema_20"
    capital: 100000

  - name: "MFI_Reversal_Strategy"
    long: "mfi < 20 & mfi.shift(1) >= 20 & volume > volume.rolling_mean(10)"
    short: "mfi > 80 & mfi.shift(1) <= 80 & volume > volume.rolling_mean(10)"
    capital: 100000

  # ═══════════════════════════════════════════════════════════════════════════════
  # 📊 TREND FOLLOWING STRATEGIES
  # ═══════════════════════════════════════════════════════════════════════════════

  - name: "ADX_Trend_Strategy"
    long: "adx > 25 & close > ema_20 & volume > volume.rolling_mean(20)"
    short: "adx > 25 & close < ema_20 & volume > volume.rolling_mean(20)"
    capital: 100000

  - name: "EMA_Triple_Trend"
    long: "ema_5 > ema_21 & ema_21 > ema_50 & close > ema_5 & volume > volume.rolling_mean(15)"
    short: "ema_5 < ema_21 & ema_21 < ema_50 & close < ema_5 & volume > volume.rolling_mean(15)"
    capital: 100000

  - name: "SuperTrend_Trend"
    long: "close > supertrend & supertrend.shift(1) >= close.shift(1) & volume > volume.rolling_mean(10) * 1.2"
    short: "close < supertrend & supertrend.shift(1) <= close.shift(1) & volume > volume.rolling_mean(10) * 1.2"
    capital: 100000

  - name: "Donchian_Channel_Trend"
    long: "close > donchian_high.shift(1) & volume > volume.rolling_mean(20) * 1.5"
    short: "close < donchian_low.shift(1) & volume > volume.rolling_mean(20) * 1.5"
    capital: 100000

  # ═══════════════════════════════════════════════════════════════════════════════
  # 🌅 GAP & BREAKOUT STRATEGIES (Popular in Indian Market)
  # ═══════════════════════════════════════════════════════════════════════════════

  - name: "Gap_Up_Continuation"
    long: "open > close.shift(1) * 1.01 & close > open & volume > volume.rolling_mean(10) * 1.5"
    short: "open < close.shift(1) * 0.99 & close < open & volume > volume.rolling_mean(10) * 1.5"
    capital: 100000

  - name: "Morning_Star_Pattern"
    long: "low < low.shift(1) & low < low.shift(2) & close > open & close > close.shift(1)"
    short: "high > high.shift(1) & high > high.shift(2) & close < open & close < close.shift(1)"
    capital: 100000

  - name: "ATR_Breakout_Strategy"
    long: "high > high.rolling_max(20).shift(1) + atr & volume > volume.rolling_mean(20) * 1.3"
    short: "low < low.rolling_min(20).shift(1) - atr & volume > volume.rolling_mean(20) * 1.3"
    capital: 100000

  # ═══════════════════════════════════════════════════════════════════════════════
  # 🔄 COMBINATION STRATEGIES (Multi-Indicator)
  # ═══════════════════════════════════════════════════════════════════════════════

  - name: "RSI_MACD_Combo"
    long: "rsi_14 > 50 & macd > macd_signal & close > ema_20 & volume > volume.rolling_mean(15)"
    short: "rsi_14 < 50 & macd < macd_signal & close < ema_20 & volume > volume.rolling_mean(15)"
    capital: 100000

  - name: "SuperTrend_RSI_Combo"
    long: "close > supertrend & rsi_14 > 55 & ema_5 > ema_21 & volume > volume.rolling_mean(10)"
    short: "close < supertrend & rsi_14 < 45 & ema_5 < ema_21 & volume > volume.rolling_mean(10)"
    capital: 100000

  - name: "VWAP_RSI_Strategy"
    long: "close > vwap & rsi_14 > 50 & rsi_14.shift(1) <= 50 & volume > volume.rolling_mean(20)"
    short: "close < vwap & rsi_14 < 50 & rsi_14.shift(1) >= 50 & volume > volume.rolling_mean(20)"
    capital: 100000

  - name: "Bollinger_RSI_Strategy"
    long: "close > (bb_upper + bb_lower) / 2 & rsi_14 > 50 & volume > volume.rolling_mean(15)"
    short: "close < (bb_upper + bb_lower) / 2 & rsi_14 < 50 & volume > volume.rolling_mean(15)"
    capital: 100000

  # ═══════════════════════════════════════════════════════════════════════════════
  # 💹 ADVANCED INDIAN MARKET STRATEGIES
  # ═══════════════════════════════════════════════════════════════════════════════

  - name: "Pivot_Point_Strategy"
    long: "close > pivot * 1.01 & close.shift(1) <= pivot * 1.01 & volume > volume.rolling_mean(20) * 1.4"
    short: "close < pivot * 0.99 & close.shift(1) >= pivot * 0.99 & volume > volume.rolling_mean(20) * 1.4"
    capital: 100000

  - name: "Support_Resistance_Strategy"
    long: "close > resistance & close.shift(1) <= resistance & rsi_14 > 45 & volume > volume.rolling_mean(15)"
    short: "close < support & close.shift(1) >= support & rsi_14 < 55 & volume > volume.rolling_mean(15)"
    capital: 100000

  - name: "EMA_Multi_Timeframe"
    long: "ema_5 > ema_10 & ema_10 > ema_20 & ema_20 > ema_50 & volume > volume.rolling_mean(20)"
    short: "ema_5 < ema_10 & ema_10 < ema_20 & ema_20 < ema_50 & volume > volume.rolling_mean(20)"
    capital: 100000

  - name: "Money_Flow_Strategy"
    long: "mfi > 50 & mfi.shift(1) <= 50 & close > ema_20 & volume > volume.rolling_mean(15) * 1.2"
    short: "mfi < 50 & mfi.shift(1) >= 50 & close < ema_20 & volume > volume.rolling_mean(15) * 1.2"
    capital: 100000

  # ═══════════════════════════════════════════════════════════════════════════════
  # 🎯 ADDITIONAL INDIAN MARKET STRATEGIES
  # ═══════════════════════════════════════════════════════════════════════════════

  - name: "RSI_5_Extreme_Strategy"
    long: "rsi_5 < 10 & volume > volume.rolling_mean(10) * 1.5 & close > ema_5"
    short: "rsi_5 > 90 & volume > volume.rolling_mean(10) * 1.5 & close < ema_5"
    capital: 100000

  - name: "EMA_10_50_Crossover"
    long: "ema_10 > ema_50 & ema_10.shift(1) <= ema_50.shift(1) & volume > volume.rolling_mean(20) * 1.3"
    short: "ema_10 < ema_50 & ema_10.shift(1) >= ema_50.shift(1) & volume > volume.rolling_mean(20) * 1.3"
    capital: 100000

  - name: "Volume_Breakout_Strategy"
    long: "volume > volume.rolling_mean(20) * 3.0 & close > open & rsi_14 > 50"
    short: "volume > volume.rolling_mean(20) * 3.0 & close < open & rsi_14 < 50"
    capital: 100000

  - name: "Trendline_Bounce_Strategy"
    long: "close > trendline & close.shift(1) <= trendline & rsi_14 > 50 & volume > volume.rolling_mean(15)"
    short: "close < trendline & close.shift(1) >= trendline & rsi_14 < 50 & volume > volume.rolling_mean(15)"
    capital: 100000

  - name: "Regime_Based_Strategy"
    long: "regime == 'trending' & close > ema_20 & rsi_14 > 50 & volume > volume.rolling_mean(20)"
    short: "regime == 'trending' & close < ema_20 & rsi_14 < 50 & volume > volume.rolling_mean(20)"
    capital: 100000

  - name: "Log_Return_Momentum"
    long: "log_return > 0.01 & log_return.shift(1) > 0 & volume > volume.rolling_mean(10) * 1.5"
    short: "log_return < -0.01 & log_return.shift(1) < 0 & volume > volume.rolling_mean(10) * 1.5"
    capital: 100000
